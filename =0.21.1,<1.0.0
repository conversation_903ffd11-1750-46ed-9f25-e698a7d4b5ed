Collecting pytest-asyncio
  Downloading pytest_asyncio-0.26.0-py3-none-any.whl.metadata (4.0 kB)
Requirement already satisfied: pytest<9,>=8.2 in /Users/<USER>/.pyenv/versions/3.12.8/lib/python3.12/site-packages (from pytest-asyncio) (8.3.5)
Requirement already satisfied: iniconfig in /Users/<USER>/.pyenv/versions/3.12.8/lib/python3.12/site-packages (from pytest<9,>=8.2->pytest-asyncio) (2.1.0)
Requirement already satisfied: packaging in /Users/<USER>/.pyenv/versions/3.12.8/lib/python3.12/site-packages (from pytest<9,>=8.2->pytest-asyncio) (24.2)
Requirement already satisfied: pluggy<2,>=1.5 in /Users/<USER>/.pyenv/versions/3.12.8/lib/python3.12/site-packages (from pytest<9,>=8.2->pytest-asyncio) (1.6.0)
Downloading pytest_asyncio-0.26.0-py3-none-any.whl (19 kB)
Installing collected packages: pytest-asyncio
Successfully installed pytest-asyncio-0.26.0
