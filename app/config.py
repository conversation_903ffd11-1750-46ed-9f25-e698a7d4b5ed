"""
Configuration settings for the RAG Citation API.
"""
import os
from pathlib import Path
from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # API Configuration
    app_host: str = Field(default="0.0.0.0", env="APP_HOST")
    app_port: int = Field(default=8000, env="APP_PORT")
    app_debug: bool = Field(default=False, env="APP_DEBUG")
    
    # API Metadata
    api_title: str = Field(default="RAG Citation API", env="API_TITLE")
    api_description: str = Field(
        default="A REST API for Retrieval-Augmented Generation with Citations",
        env="API_DESCRIPTION"
    )
    api_version: str = Field(default="1.0.0", env="API_VERSION")
    
    # Anthropic Configuration
    anthropic_api_key: str = Field(..., env="ANTHROPIC_API_KEY")
    
    # KDB.AI Configuration
    kdbai_endpoint: str = Field(..., env="KDBAI_ENDPOINT")
    kdbai_api_key: str = Field(..., env="KDBAI_API_KEY")
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    
    # File Storage Configuration
    upload_dir: Path = Field(default=Path("./uploads"), env="UPLOAD_DIR")
    max_file_size: int = Field(default=50_000_000, env="MAX_FILE_SIZE")  # 50MB
    
    # Vector Database Configuration
    vector_db_name: str = Field(default="rag_documents", env="VECTOR_DB_NAME")
    vector_table_name: str = Field(default="document_chunks", env="VECTOR_TABLE_NAME")
    embedding_dimension: int = Field(default=1536, env="EMBEDDING_DIMENSION")
    
    # Document Processing Configuration
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")
    
    # Supported file types
    supported_file_types: set[str] = {".pdf", ".txt", ".md"}
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure upload directory exists
        self.upload_dir.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()
