"""
Document processing service using LangChain.
"""
import logging
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Tuple
from uuid import UUID, uuid4

import aiofiles
from langchain_community.document_loaders import <PERSON>yPDFLoader, TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter

from app.config import settings
from app.models import Document, DocumentChunk, DocumentMetadata
from app.services.embedding_service import EmbeddingService

logger = logging.getLogger(__name__)


class DocumentService:
    """Service for processing and managing documents."""

    def __init__(self, embedding_service: EmbeddingService):
        self.embedding_service = embedding_service
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap,
            separators=["\n\n", "\n", " ", ""]
        )

        # In-memory document storage (replace with database in production)
        self.documents: dict[UUID, Document] = {}

    async def save_uploaded_file(self, file_content: bytes, filename: str) -> Path:
        """
        Save uploaded file to disk.

        Args:
            file_content: File content as bytes
            filename: Original filename

        Returns:
            Path to saved file
        """
        try:
            # Generate unique filename
            file_id = uuid4()
            file_extension = Path(filename).suffix
            unique_filename = f"{file_id}{file_extension}"
            file_path = settings.upload_dir / unique_filename

            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)

            logger.info(f"Saved file: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"Error saving file: {str(e)}")
            raise

    def validate_file(self, filename: str, file_size: int) -> None:
        """
        Validate uploaded file.

        Args:
            filename: Original filename
            file_size: File size in bytes

        Raises:
            ValueError: If file is invalid
        """
        # Check file size
        if file_size > settings.max_file_size:
            raise ValueError(f"File size ({file_size} bytes) exceeds maximum allowed size ({settings.max_file_size} bytes)")

        # Check file extension
        file_extension = Path(filename).suffix.lower()
        if file_extension not in settings.supported_file_types:
            raise ValueError(f"File type {file_extension} not supported. Supported types: {settings.supported_file_types}")

    async def process_document(
        self,
        file_path: Path,
        filename: str,
        file_size: int,
        content_type: str
    ) -> Document:
        """
        Process uploaded document and create chunks.

        Args:
            file_path: Path to saved file
            filename: Original filename
            file_size: File size in bytes
            content_type: MIME content type

        Returns:
            Processed document
        """
        try:
            logger.info(f"Processing document: {filename}")

            # Create document metadata
            metadata = DocumentMetadata(
                filename=filename,
                file_size=file_size,
                content_type=content_type,
                upload_timestamp=datetime.utcnow()
            )

            # Create document
            document = Document(
                filename=filename,
                file_path=str(file_path),
                metadata=metadata,
                status="processing"
            )

            # Store document
            self.documents[document.id] = document

            try:
                # Load and split document
                chunks = await self._load_and_split_document(file_path, document.id)

                # Generate embeddings for chunks
                await self._generate_embeddings_for_chunks(chunks)

                # Update document status
                document.status = "completed"
                document.metadata.chunk_count = len(chunks)

                logger.info(f"Successfully processed document: {filename} ({len(chunks)} chunks)")

                return document, chunks

            except Exception as e:
                document.status = "failed"
                document.error_message = str(e)
                logger.error(f"Error processing document {filename}: {str(e)}")
                raise

        except Exception as e:
            logger.error(f"Error in process_document: {str(e)}")
            raise

    async def _load_and_split_document(self, file_path: Path, document_id: UUID) -> List[DocumentChunk]:
        """
        Load document content and split into chunks.

        Args:
            file_path: Path to document file
            document_id: Document ID

        Returns:
            List of document chunks
        """
        try:
            file_extension = file_path.suffix.lower()

            # Load document based on file type
            if file_extension == ".pdf":
                loader = PyPDFLoader(str(file_path))
                documents = loader.load()
            elif file_extension in [".txt", ".md"]:
                loader = TextLoader(str(file_path), encoding="utf-8")
                documents = loader.load()
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")

            # Split documents into chunks
            split_docs = self.text_splitter.split_documents(documents)

            # Create DocumentChunk objects
            chunks = []
            for i, doc in enumerate(split_docs):
                chunk = DocumentChunk(
                    id=f"{document_id}_{i}",
                    document_id=document_id,
                    chunk_index=i,
                    content=doc.page_content,
                    metadata={
                        "source": str(file_path),
                        "page": doc.metadata.get("page", 0),
                        **doc.metadata
                    }
                )
                chunks.append(chunk)

            logger.info(f"Split document into {len(chunks)} chunks")
            return chunks

        except Exception as e:
            logger.error(f"Error loading and splitting document: {str(e)}")
            raise

    async def _generate_embeddings_for_chunks(self, chunks: List[DocumentChunk]) -> None:
        """
        Generate embeddings for document chunks.

        Args:
            chunks: List of document chunks
        """
        try:
            logger.info(f"Generating embeddings for {len(chunks)} chunks")

            # Extract text content
            texts = [chunk.content for chunk in chunks]

            # Generate embeddings
            embeddings = await self.embedding_service.generate_embeddings(texts)

            # Assign embeddings to chunks
            for chunk, embedding in zip(chunks, embeddings):
                chunk.embedding = embedding

            logger.info(f"Successfully generated embeddings for {len(chunks)} chunks")

        except Exception as e:
            logger.error(f"Error generating embeddings for chunks: {str(e)}")
            raise

    def get_document(self, document_id: UUID) -> Optional[Document]:
        """Get document by ID."""
        return self.documents.get(document_id)

    def list_documents(self) -> List[Document]:
        """List all documents."""
        return list(self.documents.values())

    async def delete_document(self, document_id: UUID) -> bool:
        """
        Delete document and its file.

        Args:
            document_id: Document ID to delete

        Returns:
            True if successful
        """
        try:
            document = self.documents.get(document_id)
            if not document:
                return False

            # Delete file
            file_path = Path(document.file_path)
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Deleted file: {file_path}")

            # Remove from storage
            del self.documents[document_id]

            logger.info(f"Successfully deleted document: {document_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting document: {str(e)}")
            return False
