Collecting black
  Downloading black-25.1.0-cp312-cp312-macosx_11_0_arm64.whl.metadata (81 kB)
Requirement already satisfied: click>=8.0.0 in /Users/<USER>/.pyenv/versions/3.12.8/lib/python3.12/site-packages (from black) (8.1.8)
Requirement already satisfied: mypy-extensions>=0.4.3 in /Users/<USER>/.pyenv/versions/3.12.8/lib/python3.12/site-packages (from black) (1.1.0)
Requirement already satisfied: packaging>=22.0 in /Users/<USER>/.pyenv/versions/3.12.8/lib/python3.12/site-packages (from black) (24.2)
Collecting pathspec>=0.9.0 (from black)
  Downloading pathspec-0.12.1-py3-none-any.whl.metadata (21 kB)
Collecting platformdirs>=2 (from black)
  Downloading platformdirs-4.3.8-py3-none-any.whl.metadata (12 kB)
Downloading black-25.1.0-cp312-cp312-macosx_11_0_arm64.whl (1.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.5/1.5 MB 3.2 MB/s eta 0:00:00
Downloading pathspec-0.12.1-py3-none-any.whl (31 kB)
Downloading platformdirs-4.3.8-py3-none-any.whl (18 kB)
Installing collected packages: platformdirs, pathspec, black

Successfully installed black-25.1.0 pathspec-0.12.1 platformdirs-4.3.8
