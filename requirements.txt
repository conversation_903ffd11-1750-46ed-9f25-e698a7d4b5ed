# Core API Framework
fastapi>=0.104.1,<0.110.0
uvicorn[standard]>=0.24.0,<0.30.0
python-multipart>=0.0.6,<0.1.0

# Document Processing
langchain>=0.1.0,<0.3.0
langchain-community>=0.0.10,<0.3.0
langchain-text-splitters>=0.0.1,<0.3.0
pypdf>=3.17.4,<5.0.0
python-magic>=0.4.27,<0.5.0

# Vector Database
kdbai-client>=1.4.0,<2.0.0

# AI/ML Libraries
anthropic>=0.8.1,<1.0.0
openai>=1.6.1,<2.0.0
sentence-transformers>=2.2.2,<3.0.0
numpy>=1.24.3,<2.0.0

# Data Processing
pandas>=2.1.4,<3.0.0
pydantic>=2.5.2,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# Utilities
python-dotenv>=1.0.0,<2.0.0
aiofiles>=23.2.1,<24.0.0

# Development
pytest>=7.4.3,<8.0.0
pytest-asyncio>=0.21.1,<1.0.0
black>=23.12.1,<25.0.0
isort>=5.13.2,<6.0.0

# Ensure compatible setuptools and wheel
setuptools>=69.0.0,<71.0.0
wheel>=0.42.0,<1.0.0
